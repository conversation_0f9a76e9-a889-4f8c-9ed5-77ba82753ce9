@php
    $record = $getRecord();
    $average = $getState() ?? 0;
    $status = $record->brake_pad_status ?? '';
    $color = $record->brake_pad_status_color ?? 'gray';

    // Configuración de colores y estilos
    $config = match($color) {
        'success' => [
            'bg' => 'bg-success-500 dark:bg-success-400',
            'text' => 'text-success-700 dark:text-success-300',
            'badge' => 'bg-success-50 text-success-700 ring-success-600/20 dark:bg-success-400/10 dark:text-success-400 dark:ring-success-400/30',
            'glow' => 'shadow-success-500/25',
            'icon' => '✓'
        ],
        'warning' => [
            'bg' => 'bg-warning-500 dark:bg-warning-400',
            'text' => 'text-warning-700 dark:text-warning-300',
            'badge' => 'bg-warning-50 text-warning-700 ring-warning-600/20 dark:bg-warning-400/10 dark:text-warning-400 dark:ring-warning-400/30',
            'glow' => 'shadow-warning-500/25',
            'icon' => '⚠'
        ],
        'danger' => [
            'bg' => 'bg-danger-500 dark:bg-danger-400',
            'text' => 'text-danger-700 dark:text-danger-300',
            'badge' => 'bg-danger-50 text-danger-700 ring-danger-600/20 dark:bg-danger-400/10 dark:text-danger-400 dark:ring-danger-400/30',
            'glow' => 'shadow-danger-500/25',
            'icon' => '⚡'
        ],
        default => [
            'bg' => 'bg-gray-500 dark:bg-gray-400',
            'text' => 'text-gray-700 dark:text-gray-300',
            'badge' => 'bg-gray-50 text-gray-700 ring-gray-600/20 dark:bg-gray-400/10 dark:text-gray-400 dark:ring-gray-400/30',
            'glow' => 'shadow-gray-500/25',
            'icon' => '○'
        ]
    };
@endphp

<div class="fi-ta-text-item group px-3 py-4">
    <div class="flex items-center gap-4">
        {{-- Barra de progreso mejorada --}}
        <div class="flex-1 space-y-2">
            {{-- Contenedor principal --}}
            <div class="relative">
                {{-- Barra de fondo --}}
                <div class="h-3 w-full rounded-full bg-gray-200 dark:bg-gray-700 shadow-inner overflow-hidden">
                    {{-- Barra de progreso con efectos --}}
                    <div class="relative h-full rounded-full {{ $config['bg'] }} transition-all duration-700 ease-out {{ $config['glow'] }} shadow-sm"
                         style="width: {{ min(max($average, 0), 100) }}%">
                        {{-- Efecto de brillo animado --}}
                        <div class="absolute inset-0 rounded-full bg-gradient-to-r from-transparent via-white/30 to-transparent animate-pulse"></div>

                        {{-- Indicador de porcentaje dentro de la barra --}}
                        @if($average >= 25)
                            <div class="absolute inset-0 flex items-center justify-center">
                                <span class="text-xs font-bold text-white drop-shadow-sm">
                                    {{ number_format($average, 0) }}%
                                </span>
                            </div>
                        @endif
                    </div>
                </div>

                {{-- Marcadores de referencia --}}
                <div class="absolute -top-1 h-5 w-full pointer-events-none">
                    {{-- Marcador 30% --}}
                    <div class="absolute left-[30%] top-0 h-full w-0.5 bg-gray-400/40 dark:bg-gray-500/40"></div>
                    {{-- Marcador 70% --}}
                    <div class="absolute left-[70%] top-0 h-full w-0.5 bg-gray-400/40 dark:bg-gray-500/40"></div>
                </div>
            </div>

            {{-- Etiquetas de referencia --}}
            <div class="flex justify-between text-xs text-gray-400 dark:text-gray-500 relative">
                <span>0%</span>
                <span class="absolute left-[30%] -translate-x-1/2">30%</span>
                <span class="absolute left-[70%] -translate-x-1/2">70%</span>
                <span>100%</span>
            </div>
        </div>

        {{-- Panel de información --}}
        <div class="flex flex-col items-end gap-2 min-w-0">
            {{-- Badge de estado con icono --}}
            <div class="inline-flex items-center gap-1.5 rounded-md px-2.5 py-1 text-xs font-medium ring-1 ring-inset {{ $config['badge'] }}">
                <span class="text-sm">{{ $config['icon'] }}</span>
                <span>{{ $status }}</span>
            </div>

            {{-- Porcentaje destacado --}}
            <div class="text-right">
                <div class="text-lg font-bold tabular-nums {{ $config['text'] }}">
                    {{ number_format($average, 1) }}%
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400">
                    Promedio
                </div>
            </div>
        </div>
    </div>

    {{-- Información adicional al hacer hover --}}
    <div class="mt-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
        <div class="text-xs text-gray-500 dark:text-gray-400 space-y-1">
            <div class="flex justify-between">
                <span>Delantera Izq:</span>
                <span>{{ $record->front_left_brake_pad ?? 0 }}%</span>
            </div>
            <div class="flex justify-between">
                <span>Delantera Der:</span>
                <span>{{ $record->front_right_brake_pad ?? 0 }}%</span>
            </div>
            <div class="flex justify-between">
                <span>Trasera Izq:</span>
                <span>{{ $record->rear_left_brake_pad ?? 0 }}%</span>
            </div>
            <div class="flex justify-between">
                <span>Trasera Der:</span>
                <span>{{ $record->rear_right_brake_pad ?? 0 }}%</span>
            </div>
        </div>
    </div>
</div>