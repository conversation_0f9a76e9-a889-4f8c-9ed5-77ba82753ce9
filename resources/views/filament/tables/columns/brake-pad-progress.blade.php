@php
    $record = $getRecord();
    $average = $getState() ?? 0;
    $status = $record->brake_pad_status ?? 'Sin datos';
    $color = $record->brake_pad_status_color ?? 'gray';

    // Clases de Filament para colores de fondo de la barra
    $progressBarClass = match($color) {
        'success' => 'bg-success-500 dark:bg-success-400',
        'warning' => 'bg-warning-500 dark:bg-warning-400',
        'danger' => 'bg-danger-500 dark:bg-danger-400',
        default => 'bg-gray-500 dark:bg-gray-400'
    };

    // Clases de Filament para texto de estado
    $statusTextClass = match($color) {
        'success' => 'text-success-600 dark:text-success-400',
        'warning' => 'text-warning-600 dark:text-warning-400',
        'danger' => 'text-danger-600 dark:text-danger-400',
        default => 'text-gray-600 dark:text-gray-400'
    };

    // Clases para el badge del estado
    $badgeClass = match($color) {
        'success' => 'bg-success-50 text-success-700 ring-success-600/20 dark:bg-success-400/10 dark:text-success-400 dark:ring-success-400/30',
        'warning' => 'bg-warning-50 text-warning-700 ring-warning-600/20 dark:bg-warning-400/10 dark:text-warning-400 dark:ring-warning-400/30',
        'danger' => 'bg-danger-50 text-danger-700 ring-danger-600/20 dark:bg-danger-400/10 dark:text-danger-400 dark:ring-danger-400/30',
        default => 'bg-gray-50 text-gray-700 ring-gray-600/20 dark:bg-gray-400/10 dark:text-gray-400 dark:ring-gray-400/30'
    };
@endphp

<div class="fi-ta-text-item inline-flex items-center gap-1.5 px-3 py-4">
    <div class="min-w-0 flex-1">
        {{-- Barra de progreso con estilo Filament --}}
        <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700 mb-2 overflow-hidden">
            <div class="{{ $progressBarClass }} h-2.5 rounded-full transition-all duration-500 ease-out relative"
                 style="width: {{ min($average, 100) }}%">
                {{-- Efecto de brillo --}}
                <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
            </div>
        </div>

        {{-- Información del estado --}}
        <div class="flex items-center justify-between">
   

            {{-- Porcentaje --}}
            <span class="text-sm font-medium tabular-nums {{ $statusTextClass }}">
                {{ number_format($average, 1) }}%
            </span>
        </div>

   
    </div>
</div>