@php
    $record = $getRecord();
    $average = $getState() ?? 0;
    $color = $record->brake_pad_status_color ?? 'gray';

    // Colores minimalistas
    $barColor = match($color) {
        'success' => 'bg-emerald-500',
        'warning' => 'bg-amber-500',
        'danger' => 'bg-red-500',
        default => 'bg-slate-400'
    };
@endphp

<div class="flex items-center gap-3 px-1 py-2">
    {{-- Barra de progreso minimalista --}}
    <div class="flex-1 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
        <div class="{{ $barColor }} h-full rounded-full transition-all duration-300 ease-out"
             style="width: {{ min($average, 100) }}%">
        </div>
    </div>

    {{-- Porcentaje compacto --}}
    <span class="text-sm font-medium text-gray-700 dark:text-gray-300 tabular-nums min-w-[3rem] text-right">
        {{ number_format($average, 0) }}%
    </span>
</div>