@php
    $record = $getRecord();
    $average = $getState() ?? 0;
    $status = $record->brake_pad_status ?? 'Sin datos';
    $color = $record->brake_pad_status_color ?? 'gray';

    // Clases de color para la barra de progreso
    $progressBarValueClass = match($color) {
        'success' => 'bg-success-600 dark:bg-success-500',
        'warning' => 'bg-warning-600 dark:bg-warning-500',
        'danger' => 'bg-danger-600 dark:bg-danger-500',
        default => 'bg-primary-600 dark:bg-primary-500'
    };

    // Clases para el texto del estado
    $statusTextClass = match($color) {
        'success' => 'text-success-600 dark:text-success-400',
        'warning' => 'text-warning-600 dark:text-warning-400',
        'danger' => 'text-danger-600 dark:text-danger-400',
        default => 'text-gray-600 dark:text-gray-400'
    };
@endphp

<style>
.progress-bar {
    @apply h-4 w-full bg-gray-100 dark:bg-gray-800 rounded-full shadow-inner overflow-hidden;
}

.progress-bar-value {
    @apply flex h-full items-center justify-center rounded-r-full shadow-inner text-xs font-bold text-white transition-all duration-500 ease-out;
}
</style>

<div class="fi-ta-text-item px-3 py-4">
    <div class="space-y-2">
        {{-- Barra de progreso usando tus estilos personalizados --}}
        <div class="progress-bar">
            <div class="progress-bar-value {{ $progressBarValueClass }}"
                 style="width: {{ min($average, 100) }}%">
                {{-- Mostrar porcentaje solo si hay suficiente espacio --}}
                @if($average >= 20)
                    {{ number_format($average, 1) }}%
                @endif
            </div>
        </div>

        {{-- Información del estado debajo de la barra --}}
        <div class="flex items-center justify-between">
            {{-- Estado textual --}}
            <span class="text-sm font-medium {{ $statusTextClass }}">
                {{ $status }}
            </span>

            {{-- Porcentaje (siempre visible) --}}
            <span class="text-sm font-bold tabular-nums {{ $statusTextClass }}">
                {{ number_format($average, 1) }}%
            </span>
        </div>

        {{-- Indicadores de referencia opcionales --}}
        <div class="flex justify-between text-xs text-gray-400 dark:text-gray-500">
            <span>Crítico: &lt;30%</span>
            <span>Regular: 30-70%</span>
            <span>Bueno: &gt;70%</span>
        </div>
    </div>
</div>