@php
    $record = $getRecord();
    $average = $record->average_brake_pad ?? 0;
    $status = $record->brake_pad_status ?? 'Sin datos';
    $color = $record->brake_pad_status_color ?? 'gray';
    
    // Determinar la clase CSS del color
    $colorClass = match($color) {
        'success' => 'bg-green-500',
        'warning' => 'bg-yellow-500', 
        'danger' => 'bg-red-500',
        default => 'bg-gray-500'
    };
    
    $textColorClass = match($color) {
        'success' => 'text-green-700',
        'warning' => 'text-yellow-700',
        'danger' => 'text-red-700', 
        default => 'text-gray-700'
    };
@endphp

<div class="flex flex-col space-y-1">
    {{-- Barra de progreso --}}
    <div class="w-full bg-gray-200 rounded-full h-4 dark:bg-gray-700">
        <div class="{{ $colorClass }} h-4 rounded-full transition-all duration-300 flex items-center justify-center" 
             style="width: {{ min($average, 100) }}%">
            @if($average > 15)
                <span class="text-xs font-medium text-white">{{ $average }}%</span>
            @endif
        </div>
    </div>
    
    {{-- Texto del estado --}}
    <div class="flex justify-between items-center text-xs">
        <span class="font-medium {{ $textColorClass }}">{{ $status }}</span>
        @if($average <= 15)
            <span class="text-gray-600 dark:text-gray-400">{{ $average }}%</span>
        @endif
    </div>
</div>
