@php
    $record = $getRecord();
    $average = $getState() ?? 0;
    $status = $record->brake_pad_status ?? 'Sin datos';
    $color = $record->brake_pad_status_color ?? 'gray';

    // Configuración de colores usando el sistema de Filament
    $colorConfig = match($color) {
        'success' => [
            'bg' => 'fi-color-success',
            'text' => 'fi-color-success',
            'ring' => 'ring-success-600/10 dark:ring-success-400/30',
            'badge' => 'fi-badge-color-success'
        ],
        'warning' => [
            'bg' => 'fi-color-warning',
            'text' => 'fi-color-warning',
            'ring' => 'ring-warning-600/10 dark:ring-warning-400/30',
            'badge' => 'fi-badge-color-warning'
        ],
        'danger' => [
            'bg' => 'fi-color-danger',
            'text' => 'fi-color-danger',
            'ring' => 'ring-danger-600/10 dark:ring-danger-400/30',
            'badge' => 'fi-badge-color-danger'
        ],
        default => [
            'bg' => 'fi-color-gray',
            'text' => 'fi-color-gray',
            'ring' => 'ring-gray-600/10 dark:ring-gray-400/30',
            'badge' => 'fi-badge-color-gray'
        ]
    };
@endphp

<div class="fi-ta-text-item px-3 py-4">
    <div class="flex items-center gap-3">
        {{-- Barra de progreso principal --}}
        <div class="flex-1 space-y-2">
            {{-- Contenedor de la barra --}}
            <div class="relative">
                <div class="fi-progress-bar overflow-hidden rounded-full bg-gray-200 dark:bg-white/5">
                    <div class="fi-progress-bar-indicator h-2 rounded-full transition-all duration-700 ease-out {{ $colorConfig['bg'] }}"
                         style="width: {{ min($average, 100) }}%"
                         role="progressbar"
                         aria-valuenow="{{ $average }}"
                         aria-valuemin="0"
                         aria-valuemax="100">
                        {{-- Efecto de animación sutil --}}
                        <div class="h-full w-full rounded-full bg-gradient-to-r from-white/0 via-white/20 to-white/0 animate-pulse"></div>
                    </div>
                </div>

                {{-- Indicadores de umbral --}}
                <div class="absolute top-0 h-2 w-full">
                    {{-- Línea de umbral crítico (30%) --}}
                    <div class="absolute left-[30%] top-0 h-full w-px bg-gray-400/50 dark:bg-gray-500/50"></div>
                    {{-- Línea de umbral bueno (70%) --}}
                    <div class="absolute left-[70%] top-0 h-full w-px bg-gray-400/50 dark:bg-gray-500/50"></div>
                </div>
            </div>

            {{-- Etiquetas de umbral --}}
            <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400">
                <span>0%</span>
                <span class="absolute left-[30%] -translate-x-1/2">30%</span>
                <span class="absolute left-[70%] -translate-x-1/2">70%</span>
                <span>100%</span>
            </div>
        </div>

        {{-- Panel de información --}}
        <div class="flex flex-col items-end gap-1">
            {{-- Badge de estado --}}
            <span class="fi-badge fi-badge-size-sm {{ $colorConfig['badge'] }} inline-flex items-center gap-x-1 rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset {{ $colorConfig['ring'] }}">
                {{-- Icono de estado --}}
                @if($color === 'success')
                    <svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clip-rule="evenodd" />
                    </svg>
                @elseif($color === 'warning')
                    <svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.485 2.495c.673-1.167 2.357-1.167 3.03 0l6.28 10.875c.673 1.167-.17 2.625-1.516 2.625H3.72c-1.347 0-2.189-1.458-1.515-2.625L8.485 2.495zM10 5a.75.75 0 01.75.75v3.5a.75.75 0 01-1.5 0v-3.5A.75.75 0 0110 5zm0 9a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                    </svg>
                @elseif($color === 'danger')
                    <svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clip-rule="evenodd" />
                    </svg>
                @endif
                {{ $status }}
            </span>

            {{-- Porcentaje destacado --}}
            <span class="fi-ta-text-item-label text-sm font-semibold tabular-nums {{ $colorConfig['text'] }}">
                {{ number_format($average, 1) }}%
            </span>
        </div>
    </div>
</div>