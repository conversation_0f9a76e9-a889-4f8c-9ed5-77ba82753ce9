@php
    $record = $getRecord();
    $average = $getState() ?? 0;
    $status = $record->brake_pad_status ?? '';
    $color = $record->brake_pad_status_color ?? 'gray';

    // Colores usando el sistema de Filament
    $progressColor = match($color) {
        'success' => 'fi-color-success',
        'warning' => 'fi-color-warning',
        'danger' => 'fi-color-danger',
        default => 'fi-color-primary'
    };

    $textColor = match($color) {
        'success' => 'text-success-600 dark:text-success-400',
        'warning' => 'text-warning-600 dark:text-warning-400',
        'danger' => 'text-danger-600 dark:text-danger-400',
        default => 'text-primary-600 dark:text-primary-400'
    };
@endphp

<div class="fi-ta-text-item inline-flex items-center gap-x-3 px-3 py-4">
    {{-- Barra de progreso con estilo Filament --}}
    <div class="flex-1 min-w-0">
        <div class="fi-progress-bar relative overflow-hidden rounded-full bg-gray-950/5 dark:bg-white/5">
            <div class="fi-progress-bar-indicator h-1.5 rounded-full transition-all duration-500 ease-in-out {{ $progressColor }}"
                 style="width: {{ min(max($average, 0), 100) }}%"
                 role="progressbar"
                 aria-valuenow="{{ $average }}"
                 aria-valuemin="0"
                 aria-valuemax="100">
            </div>
        </div>
    </div>

    {{-- Información compacta --}}
    <div class="flex items-center gap-x-2 text-sm">
        {{-- Porcentaje --}}
        <span class="fi-ta-text-item-label font-medium tabular-nums {{ $textColor }}">
            {{ number_format($average, 1) }}%
        </span>

        {{-- Separador sutil --}}
        <span class="text-gray-400 dark:text-gray-500">•</span>

        {{-- Estado --}}
        <span class="fi-ta-text-item-label text-gray-500 dark:text-gray-400 text-xs font-medium">
            {{ $status }}
        </span>
    </div>
</div>