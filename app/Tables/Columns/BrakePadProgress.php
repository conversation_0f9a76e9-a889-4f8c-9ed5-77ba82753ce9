<?php

namespace App\Tables\Columns;

use Filament\Tables\Columns\ViewColumn;

class BrakePadProgress extends ViewColumn
{
    protected string $view = 'filament.tables.columns.brake-pad-progress';

    protected function setUp(): void
    {
        parent::setUp();

        $this->state(function ($record) {
            // Validar que el record tenga el método average_brake_pad
            if (!$record || !method_exists($record, 'getAverageBrakePadAttribute')) {
                return 0.0;
            }

            // Obtener el promedio y asegurar que sea un número válido
            $average = $record->average_brake_pad ?? 0;

            // Asegurar que esté en el rango 0-100
            return (float) max(0, min(100, $average));
        });
    }
}
